package so.appio.app.ui.widgets

import android.appwidget.AppWidgetManager
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.glance.ColorFilter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.action.ActionParameters
import androidx.glance.action.actionParametersOf
import androidx.glance.action.actionStartActivity
import androidx.glance.action.clickable
import androidx.glance.appwidget.cornerRadius
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.size
import androidx.glance.text.Text
import so.appio.app.R
import so.appio.app.widgets.AppioWidgetConfigActivity
import androidx.glance.LocalSize
import androidx.glance.background
import androidx.glance.layout.Row
import androidx.glance.layout.width
import androidx.glance.text.FontWeight
import androidx.glance.text.TextStyle
import so.appio.app.ui.screens.MainViewModel
import so.appio.app.widgets.AppioWidget

@Composable
fun WidgetZeroStateContent(appWidgetId: Int) {
    val widgetIdKey = ActionParameters.Key<Int>(AppWidgetManager.EXTRA_APPWIDGET_ID)
    val size = LocalSize.current

    Log.d(AppioWidget.TAG_UI + ":WidgetZeroStateContent", "Rendering zero state for widget id $appWidgetId with size: w= ${size.width} - h= ${size.height}")

    Box(
        modifier = GlanceModifier
            .fillMaxSize()
            .background(GlanceTheme.colors.background)
            .cornerRadius(16.dp)
            .padding(16.dp)
            .clickable(
                actionStartActivity<AppioWidgetConfigActivity>(
                    parameters = actionParametersOf(widgetIdKey to appWidgetId)
                )
            ),
        contentAlignment = Alignment.Center,
    ) {
        when {
            // 1 x Any
            size.width <= 150.dp -> {
                Log.d(AppioWidget.TAG_UI + ":WidgetZeroStateContent", "Rendering 1-Any zero state")
                Icon()
            }
            // Any x 1
            size.height <= 150.dp -> {
                Log.d(AppioWidget.TAG_UI + ":WidgetZeroStateContent", "Rendering Any-1 zero state")
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon()
                    Spacer(modifier = GlanceModifier.width(8.dp))
                    Header()
                }
            }
            else -> {
                Log.d(AppioWidget.TAG_UI + ":WidgetZeroStateContent", "Rendering full zero state")
                Column() {
                    Icon()
                    Spacer(modifier = GlanceModifier.height(8.dp))
                    Header()
                    Spacer(modifier = GlanceModifier.height(8.dp))
                    Body()
                }
            }
        }
    }
}

@Composable
fun Icon() {
    Image(
        provider = ImageProvider(R.drawable.widget_hand),
        contentDescription = "Touch to configure",
        modifier = GlanceModifier.size(32.dp),
        colorFilter = ColorFilter.tint(GlanceTheme.colors.onBackground)
    )
}

@Composable
fun Header() {
    Text(
        text = "Tap to Configure",
        style = TextStyle(
            color = GlanceTheme.colors.onBackground,
            fontWeight = FontWeight.Bold,
            fontSize = 18.sp,
        )
    )
}

@Composable
fun Body() {
    Text(
        text = "The widget template.",
        style = TextStyle(
            color = GlanceTheme.colors.onBackground,
            fontWeight = FontWeight.Normal,
            fontSize = 14.sp,
        )
    )
}