package so.appio.app.ui.widgets

import android.content.ComponentName
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceModifier
import androidx.glance.LocalContext
import androidx.glance.LocalSize
import androidx.glance.action.ActionParameters
import androidx.glance.action.actionParametersOf
import androidx.glance.action.actionStartActivity
import androidx.glance.action.clickable
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Box
import androidx.glance.layout.Spacer
import androidx.glance.layout.height
import androidx.glance.layout.width
import androidx.glance.layout.size
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.fillMaxHeight
import androidx.glance.layout.fillMaxSize
import androidx.glance.appwidget.cornerRadius
import androidx.glance.text.Text
import androidx.glance.text.TextAlign
import androidx.glance.text.TextStyle
import androidx.glance.GlanceTheme.colors
import so.appio.app.MainActivity
import so.appio.app.data.entity.widget.WidgetElement
import so.appio.app.data.entity.widget.WidgetTemplate
import so.appio.app.utils.IntentHandler
import androidx.glance.ButtonDefaults
import androidx.glance.ImageProvider
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.components.CircleIconButton
import androidx.glance.appwidget.components.FilledButton
import androidx.glance.appwidget.components.OutlineButton
import androidx.glance.appwidget.components.SquareIconButton
import androidx.glance.layout.padding
import so.appio.app.widgets.WidgetButtonAction
import so.appio.app.R
import so.appio.app.data.entity.widget.PaddingProperties
import so.appio.app.widgets.AppioWidget
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun WidgetContent(template: WidgetTemplate, serviceId: String) {

    // Get the current widget size
    val size = LocalSize.current

    // Pick the appropriate variant based on size
    val variant = template.pickVariant(size.width, size.height)

    if (variant == null) {
        NoVariant()
        return
    }
    Log.d(AppioWidget.TAG_UI + ":Widget", "Rendering template with ${variant.elements.size} elements for size: ${size.width}x${size.height}")

    val backgroundColor = variant.properties.background?.let { convertColorProvider(it) }
    val context = LocalContext.current
    val url = variant.properties.url ?: ""

    Column(
        modifier = GlanceModifier
            .conditional(backgroundColor != null) {
                background(backgroundColor!!)
            }
            .clickable(
                actionStartActivity(
                    ComponentName(context, MainActivity::class.java),
                    parameters = actionParametersOf(
                        ActionParameters.Key<String>(IntentHandler.INTENT_ACTION) to IntentHandler.IntentAction.WIDGET.value,
                        ActionParameters.Key<String>(IntentHandler.KEY_SERVICE_ID) to serviceId,
                        ActionParameters.Key<String>(IntentHandler.KEY_URL) to url,
                    )
                )
            )
    ) {
        variant.elements.forEach { element ->
            RenderElement(element, serviceId)
        }
    }
}

@Composable
private fun NoVariant() {
    Text(
        text = "No widget template found for this size.",
        style = TextStyle(
            color = colors.error
        )
    )
}

@Composable
private fun RenderElement(element: WidgetElement, serviceId: String) {
    when (element.type) {
        "box" -> RenderBox(element, serviceId)
        "column" -> RenderColumn(element, serviceId)
        "image" -> RenderImage(element, serviceId)
        "gauge" -> RenderGauge(element, serviceId)
        "row" -> RenderRow(element, serviceId)
        "spacer" -> RenderSpacer(element, serviceId)
        "text" -> RenderText(element, serviceId)
        "button" -> RenderButton(element, serviceId)
        "refreshButton" -> RenderRefreshButton(element, serviceId)
        "lastUpdated" -> RenderLastUpdated(element, serviceId)
        else -> {
            Log.d(AppioWidget.TAG_UI + ":Widget", "Unknown element type: ${element.type}")
        }
    }
}

@Composable
private fun RenderRow(element: WidgetElement, serviceId: String) {
    val rowProperties = parseRowProperties(element.properties)
    val backgroundColor = rowProperties?.background?.let { convertColorProvider(it) }

    Row(
        verticalAlignment = convertVerticalAlignment(rowProperties?.verticalAlignment),
        horizontalAlignment = convertHorizontalAlignment(rowProperties?.horizontalAlignment),
        modifier = GlanceModifier
            .conditional(backgroundColor != null) {
                background(backgroundColor!!)
            }
            .cornerRadius(0) // set default
            .conditional(rowProperties?.cornerRadius != null) {
                cornerRadius(rowProperties!!.cornerRadius!!.dp)
            }
            .padding(0) // set default
            .conditional(rowProperties?.padding != null) {
                then(convertPadding(rowProperties?.padding))
            }
            .let { modifier ->
                val widthValue = parseSizeValue(rowProperties?.width)
                when {
                    widthValue?.isMax == true -> modifier.fillMaxWidth()
                    widthValue?.value != null -> modifier.width(widthValue.value.dp)
                    else -> modifier
                }
            }
            .let { modifier ->
                val heightValue = parseSizeValue(rowProperties?.height)
                when {
                    heightValue?.isMax == true -> modifier.fillMaxHeight()
                    heightValue?.value != null -> modifier.height(heightValue.value.dp)
                    else -> modifier
                }
            }
    ) {
        element.elements.forEachIndexed { index, childElement ->
            if (index > 0 && rowProperties?.spacing != null) {
                Spacer(modifier = GlanceModifier.width(rowProperties.spacing.dp))
            }
            RenderElement(childElement, serviceId)
        }
    }
}

@Composable
private fun RenderColumn(element: WidgetElement, serviceId: String) {
    val columnProperties = parseColumnProperties(element.properties)
    val backgroundColor = columnProperties?.background?.let { convertColorProvider(it) }

    Column(
        verticalAlignment = convertVerticalAlignment(columnProperties?.verticalAlignment),
        horizontalAlignment = convertHorizontalAlignment(columnProperties?.horizontalAlignment),
        modifier = GlanceModifier
            .conditional(backgroundColor != null) {
                background(backgroundColor!!)
            }
            .cornerRadius(0) // set default
            .conditional(columnProperties?.cornerRadius != null) {
                cornerRadius(columnProperties!!.cornerRadius!!.dp)
            }
            .padding(0) // set default
            .conditional(columnProperties?.padding != null) {
                then(convertPadding(columnProperties?.padding))
            }
            .let { modifier ->
                val widthValue = parseSizeValue(columnProperties?.width)
                when {
                    widthValue?.isMax == true -> modifier.fillMaxWidth()
                    widthValue?.value != null -> modifier.width(widthValue.value.dp)
                    else -> modifier
                }
            }
            .let { modifier ->
                val heightValue = parseSizeValue(columnProperties?.height)
                when {
                    heightValue?.isMax == true -> modifier.fillMaxHeight()
                    heightValue?.value != null -> modifier.height(heightValue.value.dp)
                    else -> modifier
                }
            }
    ) {
        element.elements.forEachIndexed { index, childElement ->
            if (index > 0 && columnProperties?.spacing != null) {
                Spacer(modifier = GlanceModifier.height(columnProperties.spacing.dp))
            }
            RenderElement(childElement, serviceId)
        }
    }
}

@Composable
private fun RenderBox(element: WidgetElement, serviceId: String) {
    val boxProperties = parseBoxProperties(element.properties)
    val backgroundColor = boxProperties?.background?.let { convertColorProvider(it) }

    Box(
        contentAlignment = convertBoxAlignment(boxProperties?.horizontalAlignment, boxProperties?.verticalAlignment),
        modifier = GlanceModifier
            .conditional(backgroundColor != null) {
                background(backgroundColor!!)
            }
            .cornerRadius(0) // set default
            .conditional(boxProperties?.cornerRadius != null) {
                cornerRadius(boxProperties!!.cornerRadius!!.dp)
            }
            .padding(0) // set default
            .conditional(boxProperties?.padding != null) {
                then(convertPadding(boxProperties?.padding))
            }
            .let { modifier ->
                val widthValue = parseSizeValue(boxProperties?.width)
                when {
                    widthValue?.isMax == true -> modifier.fillMaxWidth()
                    widthValue?.value != null -> modifier.width(widthValue.value.dp)
                    else -> modifier
                }
            }
            .let { modifier ->
                val heightValue = parseSizeValue(boxProperties?.height)
                when {
                    heightValue?.isMax == true -> modifier.fillMaxHeight()
                    heightValue?.value != null -> modifier.height(heightValue.value.dp)
                    else -> modifier
                }
            }
    ) {
        element.elements.forEach { childElement ->
            RenderElement(childElement, serviceId)
        }
    }
}

// Common interface for text-like properties
private interface TextLikeProperties {
    val fontSize: Int?
    val fontWeight: String?
    val alignment: String?
    val color: String?
    val background: String?
    val padding: PaddingProperties?
    val width: kotlinx.serialization.json.JsonElement?
    val height: kotlinx.serialization.json.JsonElement?
}

// Common interface for button-like properties
private interface ButtonLikeProperties {
    val text: String?
    val color: String?
    val tint: String?
    val style: String?
    val padding: PaddingProperties?
    val width: kotlinx.serialization.json.JsonElement?
    val height: kotlinx.serialization.json.JsonElement?
    val url: String?
}

@Composable
private fun RenderTextWithProperties(properties: TextLikeProperties?, text: String) {
    val backgroundColor = properties?.background?.let { convertColorProvider(it) }

    Text(
        text = text,
        style = TextStyle(
            fontSize = properties?.fontSize?.sp,
            fontWeight = properties?.fontWeight?.let { convertFontWeight(it) },
            textAlign = properties?.alignment?.let { convertTextAlign(it) } ?: TextAlign.Start
        ).let { style ->
            properties?.color?.let {
                style.copy(color = convertColorProvider(it))
            } ?: style
        },
        modifier = GlanceModifier
            .conditional(backgroundColor != null) {
                background(backgroundColor!!)
            }
            .padding(0) // set default
            .conditional(properties?.padding != null) {
                then(convertPadding(properties?.padding))
            }
            .let { modifier ->
                val widthValue = parseSizeValue(properties?.width)
                when {
                    widthValue?.isMax == true -> modifier.fillMaxWidth()
                    widthValue?.value != null -> modifier.width(widthValue.value.dp)
                    else -> modifier
                }
            }
            .let { modifier ->
                val heightValue = parseSizeValue(properties?.height)
                when {
                    heightValue?.isMax == true -> modifier.fillMaxHeight()
                    heightValue?.value != null -> modifier.height(heightValue.value.dp)
                    else -> modifier
                }
            }
    )
}

@Composable
private fun RenderText(element: WidgetElement, serviceId: String) {
    val textProperties = parseTextProperties(element.properties)
    val textPropertiesAdapter = textProperties?.let {
        object : TextLikeProperties {
            override val fontSize = it.fontSize
            override val fontWeight = it.fontWeight
            override val alignment = it.alignment
            override val color = it.color
            override val background = it.background
            override val padding = it.padding
            override val width = it.width
            override val height = it.height
        }
    }

    RenderTextWithProperties(textPropertiesAdapter, textProperties?.text ?: "")
}

@Composable
private fun RenderImage(element: WidgetElement, serviceId: String) {
    val imageProperties = parseImageProperties(element.properties)

    GlanceCachedImage(
        filename = imageProperties?.__src,
        cornerRadius = imageProperties?.cornerRadius?.dp ?: 0.dp,
        modifier = GlanceModifier
            .padding(0) // set default
            .conditional(imageProperties?.padding != null) {
                then(convertPadding(imageProperties?.padding))
            }
            .let { modifier ->
                val widthValue = parseSizeValue(imageProperties?.width)
                when {
                    widthValue?.isMax == true -> modifier.fillMaxWidth()
                    widthValue?.value != null -> modifier.width(widthValue.value.dp)
                    else -> modifier
                }
            }
            .let { modifier ->
                val heightValue = parseSizeValue(imageProperties?.height)
                when {
                    heightValue?.isMax == true -> modifier.fillMaxHeight()
                    heightValue?.value != null -> modifier.height(heightValue.value.dp)
                    else -> modifier
                }
            }
    )
}

@Composable
private fun RenderGauge(element: WidgetElement, serviceId: String) {
    val gaugeProperties = parseGaugeProperties(element.properties)
    val sizeValue = parseSizeValue(gaugeProperties?.size)
    val size = sizeValue?.value?.dp ?: 150.dp // Default to 150.dp if not specified
    val useMaxSize = sizeValue?.isMax == true

    Box(
        modifier = GlanceModifier
            .padding(0) // set default
            .conditional(gaugeProperties?.padding != null) {
                then(convertPadding(gaugeProperties?.padding))
            }
    ) {
        GlanceCachedImage(
            filename = gaugeProperties?.__src,
            modifier = if (useMaxSize) {
                GlanceModifier.fillMaxSize()
            } else {
                GlanceModifier.size(size)
            }
        )

        if (gaugeProperties?.label != null) {
            Column(
                modifier = if (useMaxSize) {
                    GlanceModifier.fillMaxSize()
                } else {
                    GlanceModifier.size(size)
                },
                verticalAlignment = Alignment.CenterVertically,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = gaugeProperties.label,
                    style = TextStyle(
                        fontSize = gaugeProperties.fontSize?.sp,
                        fontWeight = gaugeProperties.fontWeight?.let { convertFontWeight(it) },
                    ).let { style ->
                        gaugeProperties.color?.let {
                            style.copy(color = convertColorProvider(it))
                        } ?: style
                    },
                )
            }
        }
    }
}

@Composable
private fun RenderSpacer(element: WidgetElement, serviceId: String) {
    val spacerProperties = parseSpacerProperties(element.properties)

    Spacer(
        modifier = GlanceModifier
            .let { modifier ->
                val lengthValue = parseSizeValue(spacerProperties?.length)
                when {
                    lengthValue?.isMax == true -> modifier.fillMaxSize()
                    lengthValue?.value != null -> modifier.size(lengthValue.value.dp)
                    else -> modifier
                }
            }
            .padding(0) // set default
            .conditional(spacerProperties?.padding != null) {
                then(convertPadding(spacerProperties?.padding))
            }
    )
}

@Composable
private fun RenderRefreshButton(element: WidgetElement, serviceId: String) {
    val buttonProperties = parseRefreshButtonProperties(element.properties)
    val buttonPropertiesAdapter = buttonProperties?.let {
        object : ButtonLikeProperties {
            override val text = it.text
            override val color = it.color
            override val tint = it.tint
            override val style = it.style
            override val padding = it.padding
            override val width = it.width
            override val height = it.height
            override val url = null // No URL for refresh buttons - will just refresh widget
        }
    }

    RenderButtonWithProperties(
        properties = buttonPropertiesAdapter,
        defaultText = "Refresh",
        serviceId = serviceId
    )
}

@Composable
private fun RenderButtonWithProperties(
    properties: ButtonLikeProperties?,
    defaultText: String,
    serviceId: String
) {
    val buttonText = properties?.text ?: defaultText
    val contentColor = properties?.color?.let { convertColorProvider(it) } ?: colors.onPrimary
    val backgroundColor = properties?.tint?.let { convertColorProvider(it) } ?: colors.primary
    val style = properties?.style ?: "filled"
    val padding = properties?.padding ?: PaddingProperties(4, 4, 4, 4)
    val url = properties?.url ?: ""

    val onClick = actionRunCallback<WidgetButtonAction>(
        parameters = actionParametersOf(
            ActionParameters.Key<String>(WidgetButtonAction.URL_PARAMETER) to url,
            ActionParameters.Key<String>(WidgetButtonAction.SERVICE_ID_PARAMETER) to serviceId
        )
    )

    val modifier = GlanceModifier
        .then(convertPadding(padding))
        .let { modifier ->
            val widthValue = parseSizeValue(properties?.width)
            when {
                widthValue?.isMax == true -> modifier.fillMaxWidth()
                widthValue?.value != null -> modifier.width(widthValue.value.dp)
                else -> modifier
            }
        }
        .let { modifier ->
            val heightValue = parseSizeValue(properties?.height)
            when {
                heightValue?.isMax == true -> modifier.fillMaxHeight()
                heightValue?.value != null -> modifier.height(heightValue.value.dp)
                else -> modifier
            }
        }

    Log.d(AppioWidget.TAG_UI + ":Widget", "Button: $style, $buttonText")

    when (style) {
        "filled" -> {
            FilledButton(
                text = buttonText,
                onClick = onClick,
                modifier = modifier,
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = backgroundColor,
                    contentColor = contentColor
                )
            )
        }
        "outline" -> {
            OutlineButton(
                text = buttonText,
                contentColor = contentColor,
                onClick = onClick,
                modifier = modifier
            )
        }
        "icon-primary" -> {
            SquareIconButton(
                imageProvider = ImageProvider(R.drawable.ic_refresh),
                contentDescription = buttonText,
                onClick = onClick,
                modifier = modifier,
                backgroundColor = backgroundColor,
                contentColor = contentColor
            )
        }
        "icon-secondary" -> {
            CircleIconButton(
                imageProvider = ImageProvider(R.drawable.ic_refresh),
                contentDescription = buttonText,
                onClick = onClick,
                modifier = modifier,
                backgroundColor = backgroundColor,
                contentColor = contentColor
            )
        }
        else -> {
            Text(
                text = "Unsupported button style: $style",
            )
        }
    }
}

@Composable
private fun RenderButton(element: WidgetElement, serviceId: String) {
    val buttonProperties = parseButtonProperties(element.properties)
    val buttonPropertiesAdapter = buttonProperties?.let {
        object : ButtonLikeProperties {
            override val text = it.text
            override val color = it.color
            override val tint = it.tint
            override val style = it.style
            override val padding = it.padding
            override val width = it.width
            override val height = it.height
            override val url = it.url
        }
    }

    RenderButtonWithProperties(
        properties = buttonPropertiesAdapter,
        defaultText = "Button",
        serviceId = serviceId
    )
}

@Composable
private fun RenderLastUpdated(element: WidgetElement, serviceId: String) {
    val lastUpdatedProperties = parseLastUpdatedProperties(element.properties)
    val currentTime = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
    val lastUpdatedPropertiesAdapter = lastUpdatedProperties?.let {
        object : TextLikeProperties {
            override val fontSize = it.fontSize
            override val fontWeight = it.fontWeight
            override val alignment = it.alignment
            override val color = it.color
            override val background = it.background
            override val padding = it.padding
            override val width = it.width
            override val height = it.height
        }
    }

    RenderTextWithProperties(lastUpdatedPropertiesAdapter, currentTime)
}



