package so.appio.app.widgets

import android.content.Context
import android.util.Log
import androidx.glance.appwidget.updateAll
import androidx.glance.appwidget.state.updateAppWidgetState
import androidx.glance.appwidget.state.getAppWidgetState
import androidx.glance.state.PreferencesGlanceStateDefinition
import androidx.work.CoroutineWorker
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ExistingPeriodicWorkPolicy
import so.appio.app.utils.ServiceManager
import java.util.UUID
import java.util.concurrent.TimeUnit

class AppioWidgetUpdateWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "LOG:AppioWidgetUpdateWorker"
        private const val UPDATE_INTERVAL_MINUTES = 5L // Update every 5 minutes
        private const val ONE_TIME_WORK_NAME = "AppioWidgetUpdate"
        private const val PERIODIC_WORK_NAME = "AppioWidgetPeriodicUpdate"
        private const val KEY_UPDATE_REASON = "update_reason"

        // Update reasons for debugging and analytics
        const val REASON_NOTIFICATION_RECEIVED = "notification_received"
        const val REASON_CONFIG_SAVED = "config_saved"
        const val REASON_MANUAL_REFRESH = "manual_refresh"
        const val REASON_PERIODIC = "periodic"

        /**
         * Start continuous updates (main update strategy)
         */
        fun startAutoUpdate(context: Context) {
            Log.d(TAG, "Starting widget auto-update (every $UPDATE_INTERVAL_MINUTES minutes)")
            val inputData = Data.Builder()
                .putString(KEY_UPDATE_REASON, REASON_PERIODIC)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<AppioWidgetUpdateWorker>(
                UPDATE_INTERVAL_MINUTES, TimeUnit.MINUTES
            )
                .setInputData(inputData)
                .addTag(PERIODIC_WORK_NAME)
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP, // Keep existing if already running
                periodicWorkRequest
            )
        }

        /**
         * Trigger immediate widget update for specific events (bonus responsiveness)
         */
        fun updateWidgetsForEvent(context: Context, reason: String) {
            Log.d(TAG, "Triggering immediate widget update for reason: $reason")

            val inputData = Data.Builder()
                .putString(KEY_UPDATE_REASON, reason)
                .build()

            val workRequest = OneTimeWorkRequestBuilder<AppioWidgetUpdateWorker>()
                .setInputData(inputData)
                .addTag(ONE_TIME_WORK_NAME)
                .build()

            // Use REPLACE to avoid duplicate immediate work, but don't interfere with periodic updates
            WorkManager.getInstance(context).enqueueUniqueWork(
                ONE_TIME_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                workRequest
            )
        }

        fun stopAutoUpdate(context: Context) {
            Log.d(TAG, "Stopping widget auto-update")
            // Cancel the periodic updates
            WorkManager.getInstance(context).cancelUniqueWork(PERIODIC_WORK_NAME)
            // Cancel any pending immediate event-driven updates
            WorkManager.getInstance(context).cancelUniqueWork(ONE_TIME_WORK_NAME)
        }
    }

    override suspend fun doWork(): Result {
        val updateReason = inputData.getString(KEY_UPDATE_REASON) ?: REASON_PERIODIC
        Log.d(TAG, "Updating all widgets for reason: $updateReason")

        try {
            updateAllWidgetsWithData()
            return Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error updating widgets", e)
            return Result.failure()
        }
    }

    // For each widget fetch and store updated config
    private suspend fun updateAllWidgetsWithData() {
        val glanceWidgetsGroupedByService = getGlanceWidgetsGroupedByService()

        // Fetch and update data for each service
        val serviceManager = ServiceManager(applicationContext)
        glanceWidgetsGroupedByService.forEach { (serviceId, widgetList) ->
            fetchAndUpdateWidgetsForService(serviceManager, serviceId, widgetList)
        }

        // Update all widgets UI
        AppioWidget().updateAll(applicationContext)
        Log.d(TAG, "All widgets updated - processed ${glanceWidgetsGroupedByService.size} services")
    }

    /**
     * Groups all Glance widgets by their serviceId to minimize API calls
     */
    private suspend fun getGlanceWidgetsGroupedByService(): Map<String, List<Pair<androidx.glance.GlanceId, String>>> {
        val glanceManager = androidx.glance.appwidget.GlanceAppWidgetManager(applicationContext)
        val glanceIds = glanceManager.getGlanceIds(AppioWidget::class.java)
        val glanceWidgetsGroupedByService = mutableMapOf<String, MutableList<Pair<androidx.glance.GlanceId, String>>>()

        // Read stored serviceId and widgetId from each widget's state
        glanceIds.forEach { glanceId ->
            try {
                val state = getAppWidgetState(applicationContext, PreferencesGlanceStateDefinition, glanceId)
                val serviceId = state[AppioWidget.KEY_SERVICE_ID]
                val widgetId = state[AppioWidget.KEY_WIDGET_ID]

                if (serviceId != null && widgetId != null) {
                    Log.d(TAG, "Found configured widget - serviceId: $serviceId, widgetId: $widgetId")
                    glanceWidgetsGroupedByService.getOrPut(serviceId) { mutableListOf() }.add(Pair(glanceId, widgetId))
                } else {
                    Log.d(TAG, "Skipping widget with missing configuration - serviceId: $serviceId, widgetId: $widgetId")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error reading widget state for glanceId: $glanceId", e)
            }
        }

        return glanceWidgetsGroupedByService
    }

    /**
     * Fetches service data and updates all widgets for a specific service
     */
    private suspend fun fetchAndUpdateWidgetsForService(
        serviceManager: ServiceManager,
        serviceId: String,
        widgetList: List<Pair<androidx.glance.GlanceId, String>>
    ) {
        try {
            Log.d(TAG, "Fetching data for serviceId: $serviceId with ${widgetList.size} widgets")
            val result = serviceManager.fetchAndStoreServiceWithWidgets(serviceId)

            if (result != null) {
                val (_, widgets) = result
                Log.d(TAG, "Successfully fetched service data for serviceId: $serviceId")

                // Update each widget's config
                widgetList.forEach { (glanceId, widgetId) ->
                    updateWidgetConfig(glanceId, widgetId, widgets)
                }
            } else {
                Log.w(TAG, "Failed to fetch data for serviceId: $serviceId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating widgets for serviceId: $serviceId", e)
        }
    }

    /**
     * Updates a single widget's configuration with data from the API
     */
    private suspend fun updateWidgetConfig(
        glanceId: androidx.glance.GlanceId,
        widgetId: String,
        widgets: List<so.appio.app.data.entity.widget.Widget>
    ) {
        val matchingWidget = widgets.find { it.id == widgetId }
        if (matchingWidget != null) {
            updateAppWidgetState(applicationContext, glanceId) { prefs ->
                prefs[AppioWidget.KEY_NONCE] = UUID.randomUUID().toString()
                prefs[AppioWidget.KEY_WIDGET_CONFIG] = matchingWidget.config
                Log.d(TAG, "Updated widget config for widgetId: $widgetId")
            }
        } else {
            Log.w(TAG, "Widget not found in API response - widgetId: $widgetId")
        }
    }
}