package so.appio.app.widgets

import android.appwidget.AppWidgetManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import androidx.glance.appwidget.state.updateAppWidgetState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import so.appio.app.utils.WidgetDiscoveryManager
import androidx.core.content.edit

class AppioWidgetReceiver : GlanceAppWidgetReceiver() {
    override val glanceAppWidget: GlanceAppWidget = AppioWidget()

    companion object {
        private const val TAG = "LOG:AppioWidgetReceiver"
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
        Log.d(TAG, "onEnabled - first widget enabled, starting auto-update")
        AppioWidgetUpdateWorker.startAutoUpdate(context)
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        Log.d(TAG, "onDisabled - last widget disabled, stopping auto-update")
        AppioWidgetUpdateWorker.stopAutoUpdate(context)
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        Log.d(TAG, "onDeleted: ${appWidgetIds.contentToString()}")

        // Use goAsync() for proper async handling in BroadcastReceiver
        val pendingResult = goAsync()

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val glanceManager = GlanceAppWidgetManager(context)

                appWidgetIds.forEach { appWidgetId ->
                    try {
                        // Get the GlanceId for this appWidgetId
                        val glanceId = glanceManager.getGlanceIdBy(appWidgetId)

                        // Clear the widget's configuration data
                        updateAppWidgetState(context, glanceId) { prefs ->
                            prefs.clear() // Remove all preferences for this widget
                        }

                        Log.d(TAG, "Cleared configuration for widget: $appWidgetId")
                    } catch (e: Exception) {
                        // Widget might already be deleted or invalid
                        Log.w(TAG, "Could not clear config for widget $appWidgetId: ${e.message}")
                    }
                }

                Log.d(TAG, "Finished cleaning up configurations for deleted widgets")
            } catch (e: Exception) {
                Log.e(TAG, "Error cleaning up widget configurations", e)
            } finally {
                // Always call finish() to complete the broadcast
                pendingResult?.finish()
            }
        }

        // Note: Don't cancel all updates here - onDisabled will handle it when the last widget is removed
    }

    override fun onRestored(context: Context?, oldWidgetIds: IntArray?, newWidgetIds: IntArray?) {
        super.onRestored(context, oldWidgetIds, newWidgetIds)
        Log.d(TAG, "onRestored - old: ${oldWidgetIds?.contentToString()}, new: ${newWidgetIds?.contentToString()}")

        // Restart auto-update after restore if we have widgets
        if (context != null && newWidgetIds != null && newWidgetIds.isNotEmpty()) {
            Log.d(TAG, "Restarting auto-update after widget restore")
            AppioWidgetUpdateWorker.startAutoUpdate(context)
        }
    }

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        super.onUpdate(context, appWidgetManager, appWidgetIds)
        Log.d(TAG, "onUpdate for widgets: ${appWidgetIds.contentToString()}")

        // Check if there's a pending service ID BEFORE calling handleNewlyAddedWidgets
        // (because handleNewlyAddedWidgets clears the pending service ID)
        val prefs = context.getSharedPreferences(WidgetDiscoveryManager.PREFS_NAME, Context.MODE_PRIVATE)
        val hasPendingServiceId = prefs.contains(WidgetDiscoveryManager.KEY_PENDING_SERVICE_ID)
        val pendingTimestamp = prefs.getLong(WidgetDiscoveryManager.KEY_PENDING_TIMESTAMP, 0)

        Log.d(TAG, "onUpdate called with ${appWidgetIds.size} widgets: ${appWidgetIds.contentToString()}")
        Log.d(TAG, "Has pending service ID: $hasPendingServiceId, timestamp: $pendingTimestamp")

        // Check for newly added widgets that might have preselected service IDs
        // Spray and pray. As we don't know Glance Widget ID to set service_id on when created via requestPinAppWidget
        handleNewlyAddedWidgets(context, appWidgetIds)

        // Trigger app exit if widget was added via discovery
        if (hasPendingServiceId) {
            // Check if the pending request is recent (within last 30 seconds)
            val currentTime = System.currentTimeMillis()
            val timeDiff = currentTime - pendingTimestamp

            if (timeDiff < 30000) { // 30 seconds timeout
                Log.d(TAG, "Widget was added via discovery (${timeDiff}ms ago) - exiting app")
                WidgetDiscoveryManager.onWidgetAdded(context)
            } else {
                Log.d(TAG, "Pending service ID is too old (${timeDiff}ms), clearing it")
                prefs.edit {
                    remove(WidgetDiscoveryManager.KEY_PENDING_SERVICE_ID).remove(WidgetDiscoveryManager.KEY_PENDING_TIMESTAMP) }
            }
        }

        // The auto-update worker will handle periodic updates
        // This method is called for manual updates or system-triggered updates
        // Trigger immediate update for better responsiveness
        AppioWidgetUpdateWorker.updateWidgetsForEvent(context, AppioWidgetUpdateWorker.REASON_MANUAL_REFRESH)
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        Log.d(TAG, "onReceive - action: ${intent.action}")
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle
    ) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions)
        Log.d(TAG, "onAppWidgetOptionsChanged for widget: $appWidgetId")

        // Widget size or options changed - trigger an immediate update for this widget
        // Note: onAppWidgetOptionsChanged doesn't support goAsync() like broadcast methods
        try {
            // Use the worker for consistent update handling
            AppioWidgetUpdateWorker.updateWidgetsForEvent(
                context,
                AppioWidgetUpdateWorker.REASON_MANUAL_REFRESH
            )
            Log.d(TAG, "Triggered update for widget $appWidgetId after options change")
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering widget update after options change", e)
        }
    }

    /**
     * Handle newly added widgets that might have been created via widget discovery
     */
    private fun handleNewlyAddedWidgets(context: Context, appWidgetIds: IntArray) {
        // Check if there's a pending service ID from widget discovery
        val pendingServiceId = WidgetDiscoveryManager.getPendingServiceId(context)

        if (pendingServiceId == null) {
            return
        }

        Log.d(TAG, "Found pending service ID: $pendingServiceId for newly added widgets: ${appWidgetIds.contentToString()}")

        // No need for goAsync() since onUpdate() is not a broadcast receiver method
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val glanceManager = GlanceAppWidgetManager(context)

                // Apply the pending service ID to all newly added widgets
                // (In practice, there should only be one, but this handles edge cases)
                appWidgetIds.forEach { appWidgetId ->
                    try {
                        val glanceId = glanceManager.getGlanceIdBy(appWidgetId)

                        // Store the service ID directly in the widget's state
                        updateAppWidgetState(context, glanceId) { prefs ->
                            // Only set if not already configured (no serviceId exists)
                            if (!prefs.contains(AppioWidget.KEY_SERVICE_ID)) {
                                prefs[AppioWidget.KEY_SERVICE_ID] = pendingServiceId
                                Log.d(TAG, "Stored service ID '$pendingServiceId' for widget: $appWidgetId")
                            } else {
                                Log.d(TAG, "Widget $appWidgetId already has serviceId, skipping")
                            }
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not store preselected service ID for widget $appWidgetId: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling newly added widgets", e)
            }
        }
    }
}